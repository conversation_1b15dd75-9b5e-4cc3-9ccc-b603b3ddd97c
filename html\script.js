// 器官交易系统JavaScript

let currentTargetId = null;
let currentOrgans = {};
let currentOrganStatus = {};

// 监听NUI消息
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.type) {
        case 'openOrganSelection':
            openOrganSelection(data.targetId, data.organs, data.organStatus);
            break;
        case 'closeUI':
            closeAllUI();
            break;
        case 'showProgress':
            showProgressBar(data.duration, data.text);
            break;
        case 'hideProgress':
            hideProgressBar();
            break;
        case 'showNotification':
            showNotification(data.message, data.notificationType, data.duration);
            break;
        case 'showCountdown':
            showCountdown(data.duration, data.text);
            break;
        case 'hideCountdown':
            hideCountdown();
            break;
        case 'updateStatus':
            updatePlayerStatus(data.status);
            break;
        case 'showOrganStatus':
            showOrganStatus(data.organs);
            break;
    }
});

// 打开器官选择界面
function openOrganSelection(targetId, organs, organStatus) {
    currentTargetId = targetId;
    currentOrgans = organs;
    currentOrganStatus = organStatus;

    // 调试日志
    console.log('[器官系统调试] 打开器官选择界面');
    console.log('目标ID:', targetId);
    console.log('器官配置:', organs);
    console.log('器官状态:', organStatus);

    // 更新器官列表
    updateOrganList();

    // 更新器官按钮状态
    updateOrganButtons();

    // 显示界面
    document.getElementById('organSelection').style.display = 'block';
}

// 更新器官列表
function updateOrganList() {
    const organList = document.getElementById('organList');
    organList.innerHTML = '';

    console.log('[器官系统调试] 更新器官列表');
    console.log('当前器官配置:', currentOrgans);
    console.log('当前器官状态:', currentOrganStatus);

    for (const [key, organ] of Object.entries(currentOrgans)) {
        // 处理不同数据类型的状态值
        const statusValue = currentOrganStatus[organ.item];
        let isAvailable = false;

        // 处理布尔值、数字和字符串类型
        if (typeof statusValue === 'boolean') {
            isAvailable = statusValue;
        } else if (typeof statusValue === 'number') {
            isAvailable = statusValue === 1;
        } else if (typeof statusValue === 'string') {
            isAvailable = statusValue === '1' || statusValue.toLowerCase() === 'true';
        } else {
            // 默认情况下，如果值存在且不是 0、false、'0'、'false'，则认为是可用的
            isAvailable = statusValue && statusValue !== 0 && statusValue !== '0' && statusValue !== 'false';
        }

        console.log(`[器官系统调试] 器官 ${key} (${organ.name}): 原始状态值=${statusValue} (类型: ${typeof statusValue}), 可用=${isAvailable}`);

        const organItem = document.createElement('div');
        organItem.className = `organ-item ${isAvailable ? '' : 'unavailable'}`;
        organItem.innerHTML = `
            <div class="organ-name">[${key}] ${organ.name}</div>
            <div class="organ-price">$${organ.price.toLocaleString()}</div>
            <div class="organ-status ${isAvailable ? 'available' : 'unavailable'}">
                ${isAvailable ? '✓' : '✗'}
            </div>
        `;

        if (isAvailable) {
            organItem.style.cursor = 'pointer';
            organItem.onclick = () => selectOrgan(key);
        }

        organList.appendChild(organItem);
    }
}

// 更新器官按钮状态
function updateOrganButtons() {
    const organButtons = document.querySelectorAll('.organ-btn');

    organButtons.forEach(button => {
        const organKey = button.getAttribute('data-organ');
        const organ = currentOrgans[organKey];

        if (organ && currentOrganStatus[organ.item] === 1) {
            button.classList.remove('disabled');
            button.onclick = (e) => {
                // 添加点击动画
                button.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);

                // 移除其他按钮的选中状态
                organButtons.forEach(btn => btn.classList.remove('selected'));

                // 添加选中状态
                button.classList.add('selected');

                selectOrgan(organKey);
            };

            // 添加悬停提示
            button.onmouseenter = () => showOrganDetailTooltip(button, organ, true);
            button.onmouseleave = () => hideOrganTooltip();
        } else {
            button.classList.add('disabled');
            button.onclick = null;

            // 为不可用器官添加提示
            button.onmouseenter = () => showOrganDetailTooltip(button, organ, false);
            button.onmouseleave = () => hideOrganTooltip();
        }
    });
}

// 选择器官
function selectOrgan(organKey) {
    const organ = currentOrgans[organKey];
    if (!organ || currentOrganStatus[organ.item] !== 1) {
        return;
    }
    
    // 高亮选中的器官
    document.querySelectorAll('.organ-btn').forEach(btn => {
        btn.classList.remove('selected');
    });
    
    const selectedButton = document.querySelector(`[data-organ="${organKey}"]`);
    if (selectedButton) {
        selectedButton.classList.add('selected');
    }
    
    // 解剖确认对话框
    if (confirm(`🔪 确认解剖提取 ${organ.name}？\n\n💰 器官价值: $${organ.price.toLocaleString()}\n\n⚠️  注意：此操作不可逆转！`)) {
        // 开始解剖动画
        startSurgeryAnimation(organ.name);

        // 发送解剖请求到Lua
        fetch(`https://${GetParentResourceName()}/selectOrgan`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                organ: organKey,
                targetId: currentTargetId
            })
        });
    }
}

// 开始解剖动画
function startSurgeryAnimation(organName) {
    // 显示进度条
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const progressFill = document.getElementById('progressFill');

    if (progressBar && progressText && progressFill) {
        progressText.textContent = `🔪 正在解剖 ${organName}...`;
        progressBar.style.display = 'block';

        // 模拟解剖进度
        let progress = 0;
        const interval = setInterval(() => {
            progress += 1.5;
            progressFill.style.width = progress + '%';

            // 更新进度文本
            if (progress < 30) {
                progressText.textContent = `🔪 正在切开皮肤...`;
            } else if (progress < 60) {
                progressText.textContent = `🩸 正在分离组织...`;
            } else if (progress < 90) {
                progressText.textContent = `🫀 正在提取 ${organName}...`;
            } else {
                progressText.textContent = `✅ ${organName} 提取完成！`;
            }

            if (progress >= 100) {
                clearInterval(interval);
                setTimeout(() => {
                    progressBar.style.display = 'none';
                }, 2000);
            }
        }, 80);
    }
}

// 取消手术
function cancelSurgery() {
    if (confirm('🚫 确认取消解剖手术？')) {
        fetch(`https://${GetParentResourceName()}/cancelSurgery`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                targetId: currentTargetId
            })
        });
    }
}

// 关闭所有UI
function closeAllUI() {
    document.getElementById('organSelection').style.display = 'none';
    document.getElementById('progressBar').style.display = 'none';
    document.getElementById('countdown').style.display = 'none';
    document.getElementById('organStatus').style.display = 'none';

    // 清理选中状态
    document.querySelectorAll('.organ-btn').forEach(btn => {
        btn.classList.remove('selected');
    });

    // 清理提示框
    hideOrganTooltip();
}

// 显示进度条
function showProgressBar(duration, text) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const progressFill = document.getElementById('progressFill');
    const progressPercentage = document.getElementById('progressPercentage');
    
    progressText.textContent = text;
    progressBar.style.display = 'block';
    
    let startTime = Date.now();
    
    const updateProgress = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min((elapsed / duration) * 100, 100);
        
        progressFill.style.width = progress + '%';
        progressPercentage.textContent = Math.round(progress) + '%';
        
        if (progress < 100) {
            requestAnimationFrame(updateProgress);
        } else {
            setTimeout(() => {
                hideProgressBar();
            }, 500);
        }
    };
    
    updateProgress();
}

// 隐藏进度条
function hideProgressBar() {
    document.getElementById('progressBar').style.display = 'none';
}

// 显示通知
function showNotification(message, type, duration) {
    const container = document.getElementById('notifications');
    
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    container.appendChild(notification);
    
    // 自动移除通知
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, duration);
}

// 显示倒计时
function showCountdown(duration, text) {
    const countdown = document.getElementById('countdown');
    const countdownText = document.getElementById('countdownText');
    const countdownTime = document.getElementById('countdownTime');
    
    countdownText.textContent = text;
    countdown.style.display = 'block';
    
    let endTime = Date.now() + duration;
    
    const updateCountdown = () => {
        const remaining = Math.max(0, endTime - Date.now());
        const minutes = Math.floor(remaining / 60000);
        const seconds = Math.floor((remaining % 60000) / 1000);
        
        countdownTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        if (remaining > 0) {
            requestAnimationFrame(updateCountdown);
        } else {
            hideCountdown();
        }
    };
    
    updateCountdown();
}

// 隐藏倒计时
function hideCountdown() {
    document.getElementById('countdown').style.display = 'none';
}

// 更新玩家状态
function updatePlayerStatus(status) {
    const drugStatus = document.querySelector('#drugStatus .status-value');
    const surgeryStatus = document.querySelector('#surgeryStatus .status-value');
    const lifeStatus = document.querySelector('#lifeStatus .status-value');
    
    drugStatus.textContent = status.drugged ? '被迷晕' : '正常';
    drugStatus.style.color = status.drugged ? '#ff4444' : '#00ff00';
    
    surgeryStatus.textContent = status.inSurgery ? '手术中' : '无';
    surgeryStatus.style.color = status.inSurgery ? '#ff4444' : '#00ff00';
    
    lifeStatus.textContent = status.lifeExtended ? '延长中' : '正常';
    lifeStatus.style.color = status.lifeExtended ? '#ffff00' : '#00ff00';
}

// 显示器官状态
function showOrganStatus(organs) {
    const container = document.getElementById('organStatus');
    const content = document.getElementById('organStatusContent');
    
    content.innerHTML = '';
    
    for (const [organField, status] of Object.entries(organs)) {
        if (organField === 'id' || organField === 'identifier' || organField === 'last_updated') {
            continue;
        }

        // 处理不同数据类型的状态值
        let isHealthy = false;

        // 处理布尔值、数字和字符串类型
        if (typeof status === 'boolean') {
            isHealthy = status;
        } else if (typeof status === 'number') {
            isHealthy = status === 1;
        } else if (typeof status === 'string') {
            isHealthy = status === '1' || status.toLowerCase() === 'true';
        } else {
            // 默认情况下，如果值存在且不是 0、false、'0'、'false'，则认为是健康的
            isHealthy = status && status !== 0 && status !== '0' && status !== 'false';
        }

        // 获取器官名称
        let organName = organField;
        for (const organ of Object.values(currentOrgans)) {
            if (organ.item === organField) {
                organName = organ.name;
                break;
            }
        }

        console.log(`[器官状态调试] ${organName} (${organField}): 原始值=${status} (类型: ${typeof status}), 健康=${isHealthy}`);

        const statusItem = document.createElement('div');
        statusItem.className = 'organ-status-item';
        statusItem.innerHTML = `
            <span class="organ-status-name">${organName}</span>
            <span class="organ-status-indicator ${isHealthy ? 'healthy' : 'damaged'}">
                ${isHealthy ? '✓' : '✗'}
            </span>
        `;

        content.appendChild(statusItem);
    }
    
    container.style.display = 'block';
}

// 隐藏器官状态
function hideOrganStatus() {
    document.getElementById('organStatus').style.display = 'none';
}

// 获取器官信息
function getOrganInfo(organName) {
    const organData = {
        '心脏': {
            icon: '❤️',
            description: '人体循环系统的核心器官，负责血液循环'
        },
        '肝脏': {
            icon: '🫀',
            description: '人体最大的内脏器官，负责解毒和代谢'
        },
        '肾脏': {
            icon: '🫘',
            description: '泌尿系统的重要器官，负责过滤血液'
        },
        '肺部': {
            icon: '🫁',
            description: '呼吸系统的主要器官，负责气体交换'
        },
        '胰腺': {
            icon: '🥞',
            description: '消化系统器官，分泌胰岛素和消化酶'
        },
        '脾脏': {
            icon: '🟣',
            description: '免疫系统器官，负责过滤血液和免疫功能'
        }
    };

    return organData[organName] || {
        icon: '🔴',
        description: '重要的人体器官'
    };
}

// ESC键处理
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const organSelection = document.getElementById('organSelection');
        if (organSelection && organSelection.style.display !== 'none') {
            fetch(`https://${GetParentResourceName()}/closeUI`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({})
            });
        }
    }
});

// 获取资源名称
function GetParentResourceName() {
    // 从URL中提取资源名称
    const url = window.location.href;
    const match = url.match(/nui:\/\/([^\/]+)/);
    if (match) {
        return match[1];
    }

    // 备用方案
    return 'qiguan_tudou';
}

// 显示详细器官提示
function showOrganDetailTooltip(button, organ, isAvailable) {
    // 移除现有提示
    hideOrganTooltip();

    const tooltip = document.createElement('div');
    tooltip.className = isAvailable ? 'organ-tooltip' : 'organ-tooltip unavailable';

    // 获取器官图标和描述
    const organInfo = getOrganInfo(organ.name);
    const status = isAvailable ? '可以摘除' : '已被摘除';
    const statusColor = isAvailable ? '#00ff00' : '#ff4444';
    const borderColor = isAvailable ? '#00ff00' : '#ff4444';

    tooltip.innerHTML = `
        <div style="font-size: 16px; font-weight: bold; margin-bottom: 8px; color: #ffffff;">
            ${organInfo.icon} ${organ.name}
        </div>
        <div style="font-size: 12px; margin-bottom: 8px; color: #aaaaaa; line-height: 1.3;">
            ${organInfo.description}
        </div>
        <div style="font-size: 14px; margin-bottom: 6px; color: #cccccc;">
            💰 价值: $${organ.price.toLocaleString()}
        </div>
        <div style="font-size: 14px; color: ${statusColor}; font-weight: bold;">
            ${isAvailable ? '✅' : '❌'} ${status}
        </div>
        ${isAvailable ? '<div style="font-size: 12px; color: #ffff00; margin-top: 6px;">🖱️ 点击进行解剖</div>' : ''}
    `;

    tooltip.style.cssText = `
        position: absolute;
        background: rgba(0, 0, 0, 0.95);
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        font-size: 14px;
        pointer-events: none;
        z-index: 1000;
        white-space: nowrap;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        border: 2px solid ${borderColor};
        backdrop-filter: blur(5px);
    `;

    document.body.appendChild(tooltip);

    // 定位提示框
    const rect = button.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();

    // 计算位置，确保不超出屏幕边界
    let left = rect.right + 15; // 显示在按钮右侧
    let top = rect.top + rect.height / 2 - tooltipRect.height / 2;

    // 边界检查
    if (left + tooltipRect.width > window.innerWidth - 10) {
        left = rect.left - tooltipRect.width - 15; // 显示在左侧
    }
    if (top < 10) top = 10;
    if (top + tooltipRect.height > window.innerHeight - 10) {
        top = window.innerHeight - tooltipRect.height - 10;
    }

    tooltip.style.left = left + 'px';
    tooltip.style.top = top + 'px';

    // 添加淡入动画
    tooltip.style.opacity = '0';
    tooltip.style.transform = 'scale(0.9)';
    setTimeout(() => {
        tooltip.style.transition = 'all 0.3s ease';
        tooltip.style.opacity = '1';
        tooltip.style.transform = 'scale(1)';
    }, 10);
}

// 显示简单器官提示（备用函数）
function showOrganTooltip(button, organName) {
    showOrganDetailTooltip(button, {name: organName, price: 0}, true);
}

// 隐藏器官提示
function hideOrganTooltip() {
    const tooltip = document.querySelector('.organ-tooltip');
    if (tooltip) {
        tooltip.style.opacity = '0';
        tooltip.style.transform = 'scale(0.9)';
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.remove();
            }
        }, 300);
    }
}
