<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI持久化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .organ-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        .organ-item.available {
            background-color: #e8f5e8;
            border-color: #4caf50;
        }
        .organ-item.unavailable {
            background-color: #ffeaea;
            border-color: #f44336;
            cursor: not-allowed;
        }
        .status-indicator {
            font-weight: bold;
            font-size: 18px;
        }
        .status-indicator.healthy {
            color: #4caf50;
        }
        .status-indicator.damaged {
            color: #f44336;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>器官摘除UI持久化测试</h1>
    
    <div class="test-container">
        <h2>模拟器官摘除操作</h2>
        <p>测试器官摘除后UI是否保持打开并正确更新状态</p>
        <button onclick="simulateExtraction()">模拟摘除心脏</button>
        <button onclick="simulateExtraction2()">模拟摘除肝脏</button>
        <button onclick="resetOrgans()">重置所有器官</button>
    </div>
    
    <div class="test-container">
        <h2>器官状态显示</h2>
        <div id="organDisplay"></div>
    </div>
    
    <div class="test-container">
        <h2>操作日志</h2>
        <div class="log" id="operationLog"></div>
    </div>

    <script>
        // 器官配置
        const organs = {
            A: {name: '心脏', item: 'organ_heart', price: 50000},
            B: {name: '肝脏', item: 'organ_liver', price: 30000},
            C: {name: '肾脏', item: 'organ_kidney', price: 25000},
            D: {name: '肺部', item: 'organ_lung', price: 20000},
            F: {name: '胰腺', item: 'organ_pancreas', price: 18000},
            G: {name: '脾脏', item: 'organ_spleen', price: 12000}
        };

        // 当前器官状态
        let currentOrganStatus = {
            organ_heart: 1,
            organ_liver: 1,
            organ_kidney: 1,
            organ_lung: 1,
            organ_pancreas: 1,
            organ_spleen: 1
        };

        function logOperation(message) {
            const log = document.getElementById('operationLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}<br>`;
            log.scrollTop = log.scrollHeight;
        }

        function displayOrgans() {
            const display = document.getElementById('organDisplay');
            display.innerHTML = '';
            
            for (const [key, organ] of Object.entries(organs)) {
                const statusValue = currentOrganStatus[organ.item];
                let isAvailable = false;
                
                // 使用修复后的逻辑
                if (typeof statusValue === 'boolean') {
                    isAvailable = statusValue;
                } else if (typeof statusValue === 'number') {
                    isAvailable = statusValue === 1;
                } else if (typeof statusValue === 'string') {
                    isAvailable = statusValue === '1' || statusValue.toLowerCase() === 'true';
                } else {
                    isAvailable = statusValue && statusValue !== 0 && statusValue !== '0' && statusValue !== 'false';
                }
                
                const organItem = document.createElement('div');
                organItem.className = `organ-item ${isAvailable ? 'available' : 'unavailable'}`;
                organItem.innerHTML = `
                    <span>${organ.name}</span>
                    <span class="status-indicator ${isAvailable ? 'healthy' : 'damaged'}">
                        ${isAvailable ? '✓ 可摘除' : '✗ 已摘除'}
                    </span>
                `;
                
                if (isAvailable) {
                    organItem.onclick = () => extractOrgan(key);
                }
                
                display.appendChild(organItem);
            }
        }

        function extractOrgan(organKey) {
            const organ = organs[organKey];
            if (!organ || currentOrganStatus[organ.item] !== 1) {
                return;
            }
            
            if (confirm(`确认摘除 ${organ.name}？`)) {
                // 模拟摘除操作
                currentOrganStatus[organ.item] = 0;
                
                logOperation(`摘除了 ${organ.name}，UI保持打开`);
                
                // 刷新显示（模拟 refreshOrganStatus 函数）
                displayOrgans();
                
                logOperation(`器官状态已更新，界面未关闭`);
            }
        }

        function simulateExtraction() {
            if (currentOrganStatus.organ_heart === 1) {
                currentOrganStatus.organ_heart = 0;
                logOperation('模拟摘除心脏');
                displayOrgans();
            } else {
                logOperation('心脏已经被摘除');
            }
        }

        function simulateExtraction2() {
            if (currentOrganStatus.organ_liver === 1) {
                currentOrganStatus.organ_liver = 0;
                logOperation('模拟摘除肝脏');
                displayOrgans();
            } else {
                logOperation('肝脏已经被摘除');
            }
        }

        function resetOrgans() {
            currentOrganStatus = {
                organ_heart: 1,
                organ_liver: 1,
                organ_kidney: 1,
                organ_lung: 1,
                organ_pancreas: 1,
                organ_spleen: 1
            };
            logOperation('重置所有器官状态');
            displayOrgans();
        }

        // 初始化显示
        displayOrgans();
        logOperation('UI持久化测试页面已加载');
    </script>
</body>
</html>
