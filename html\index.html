<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>器官交易系统</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- 器官选择界面 -->
    <div id="organSelection" class="ui-panel" style="display: none;">
        <div class="panel-header">
            <h2>人体器官解剖</h2>
            <button class="close-btn" onclick="cancelSurgery()">×</button>
        </div>
        
        <div class="panel-content">
            <div class="human-body">
                <img src="images/human_body.png" alt="人体图" class="body-image">
                
                <!-- 器官选择按钮 - 根据新人体图精确定位 -->
                <div class="organ-buttons">
                    <!-- A: 心脏 - 胸部中央偏右 -->
                    <button class="organ-btn" data-organ="A" style="top: 28%; left: 55%;" title="心脏">A</button>

                    <!-- B: 肝脏 - 右上腹部 -->
                    <button class="organ-btn" data-organ="B" style="top: 38%; left: 35%;" title="肝脏">B</button>

                    <!-- C: 肾脏 - 左下腹部 -->
                    <button class="organ-btn" data-organ="C" style="top: 55%; left: 25%;" title="肾脏">C</button>

                    <!-- D: 肺部 - 左上胸部 -->
                    <button class="organ-btn" data-organ="D" style="top: 25%; left: 35%;" title="肺部">D</button>

                    <!-- F: 胰腺 - 腹部中央 -->
                    <button class="organ-btn" data-organ="F" style="top: 45%; left: 50%;" title="胰腺">F</button>

                    <!-- G: 脾脏 - 右上腹部 -->
                    <button class="organ-btn" data-organ="G" style="top: 38%; left: 65%;" title="脾脏">G</button>
                </div>
            </div>
            
            <div class="organ-info">
                <h3>器官信息</h3>
                <div id="organList" class="organ-list">
                    <!-- 器官列表将通过JavaScript动态生成 -->
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-danger" onclick="cancelSurgery()">取消手术</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 进度条 -->
    <div id="progressBar" class="progress-container" style="display: none;">
        <div class="progress-content">
            <div class="progress-text" id="progressText">处理中...</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-percentage" id="progressPercentage">0%</div>
        </div>
    </div>
    
    <!-- 通知系统 -->
    <div id="notifications" class="notifications-container"></div>
    
    <!-- 倒计时显示 -->
    <div id="countdown" class="countdown-container" style="display: none;">
        <div class="countdown-content">
            <div class="countdown-text" id="countdownText">倒计时</div>
            <div class="countdown-time" id="countdownTime">00:00</div>
        </div>
    </div>
    
    
    
    <!-- 器官状态显示 -->
    <div id="organStatus" class="organ-status-container" style="display: none;">
        <div class="organ-status-header">
            <h3>器官状态</h3>
            <button class="close-btn" onclick="hideOrganStatus()">×</button>
        </div>
        <div class="organ-status-content" id="organStatusContent">
            <!-- 器官状态将通过JavaScript动态生成 -->
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
