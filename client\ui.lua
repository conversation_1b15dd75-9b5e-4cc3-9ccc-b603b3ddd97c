-- UI界面处理

local uiOpen = false
local currentMenu = nil
local currentTargetId = nil

-- 打开人体器官选择UI
function OpenOrganSelectionUI(targetId, organStatus)
    if uiOpen then return end

    uiOpen = true
    currentTargetId = targetId
    SetNuiFocus(true, true)

    SendNUIMessage({
        type = 'openOrganSelection',
        targetId = targetId,
        organs = Config.Surgery.organs,
        organStatus = organStatus
    })
end

-- 关闭UI
function CloseUI()
    if not uiOpen then return end

    uiOpen = false
    SetNuiFocus(false, false)

    -- 如果有正在进行的手术，通知服务端取消
    if currentTargetId then
        TriggerServerEvent('organ_trade:cancelSurgery', currentTargetId)
        currentTargetId = nil
    end

    SendNUIMessage({
        type = 'closeUI'
    })
end

-- NUI回调处理
RegisterNUICallback('selectOrgan', function(data, cb)
    local organKey = data.organ
    local targetId = data.targetId

    if organKey and targetId then
        TriggerServerEvent('organ_trade:extractOrgan', targetId, organKey)
    end

    -- 不关闭UI，保持界面打开以便连续操作
    cb('ok')
end)

RegisterNUICallback('cancelSurgery', function(data, cb)
    local targetId = data.targetId
    
    if targetId then
        TriggerServerEvent('organ_trade:cancelSurgery', targetId)
    end
    
    CloseUI()
    cb('ok')
end)



RegisterNUICallback('closeUI', function(data, cb)
    CloseUI()
    cb('ok')
end)

-- ESC键关闭UI
CreateThread(function()
    while true do
        Wait(0)
        if uiOpen then
            if IsControlJustPressed(0, 322) or IsControlJustReleased(0, 322) or IsDisabledControlJustPressed(0, 322) then
                CloseUI()
            end
        end
    end
end)

-- 显示进度条
function ShowProgressBar(duration, text)
    SendNUIMessage({
        type = 'showProgress',
        duration = duration,
        text = text or '处理中...'
    })
end

-- 隐藏进度条
function HideProgressBar()
    SendNUIMessage({
        type = 'hideProgress'
    })
end

-- 显示通知 (支持HTML UI和ox_lib)
function ShowNotification(message, type, duration)
    -- 使用HTML UI通知
    SendNUIMessage({
        type = 'showNotification',
        message = message,
        notificationType = type or 'info',
        duration = duration or 5000
    })

    -- 同时使用ox_lib通知作为备用
    lib.notify({
        title = '器官交易系统',
        description = message,
        type = type or 'info',
        duration = duration or 5000
    })
end

-- 显示倒计时
function ShowCountdown(duration, text)
    SendNUIMessage({
        type = 'showCountdown',
        duration = duration,
        text = text or '倒计时'
    })
end

-- 隐藏倒计时
function HideCountdown()
    SendNUIMessage({
        type = 'hideCountdown'
    })
end

-- 更新玩家状态显示
function UpdatePlayerStatus(status)
    SendNUIMessage({
        type = 'updateStatus',
        status = status
    })
end

-- 显示器官状态
function ShowOrganStatus(organs)
    SendNUIMessage({
        type = 'showOrganStatus',
        organs = organs
    })
end

-- 导出函数
exports('OpenOrganSelectionUI', OpenOrganSelectionUI)
exports('CloseUI', CloseUI)
exports('ShowProgressBar', ShowProgressBar)
exports('HideProgressBar', HideProgressBar)
exports('ShowNotification', ShowNotification)
exports('ShowCountdown', ShowCountdown)
exports('HideCountdown', HideCountdown)
exports('UpdatePlayerStatus', UpdatePlayerStatus)
exports('ShowOrganStatus', ShowOrganStatus)

print('^2[器官交易系统] ^7UI模块已加载')
