<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>器官状态测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .organ-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .organ-item.available {
            background-color: #e8f5e8;
            border-color: #4caf50;
        }
        .organ-item.unavailable {
            background-color: #ffeaea;
            border-color: #f44336;
        }
        .status-indicator {
            font-weight: bold;
            font-size: 18px;
        }
        .status-indicator.healthy {
            color: #4caf50;
        }
        .status-indicator.damaged {
            color: #f44336;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
        .debug-info {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>器官状态测试页面</h1>
    
    <div class="test-container">
        <h2>测试场景</h2>
        <button onclick="testScenario1()">场景1: 所有器官健康 (数字1)</button>
        <button onclick="testScenario2()">场景2: 所有器官健康 (字符串"1")</button>
        <button onclick="testScenario3()">场景3: 部分器官摘除 (数字0)</button>
        <button onclick="testScenario4()">场景4: 部分器官摘除 (字符串"0")</button>
        <button onclick="testScenario5()">场景5: 空数据库情况</button>
        <button onclick="testScenario6()">场景6: 所有器官健康 (布尔true) - 问题场景</button>
        <button onclick="testScenario7()">场景7: 部分器官摘除 (布尔false)</button>
    </div>
    
    <div class="test-container">
        <h2>器官状态显示</h2>
        <div id="organDisplay"></div>
        <div class="debug-info" id="debugInfo"></div>
    </div>

    <script>
        // 器官配置（模拟游戏配置）
        const organs = {
            A: {name: '心脏', item: 'organ_heart', price: 50000},
            B: {name: '肝脏', item: 'organ_liver', price: 30000},
            C: {name: '肾脏', item: 'organ_kidney', price: 25000},
            D: {name: '肺部', item: 'organ_lung', price: 20000},
            F: {name: '胰腺', item: 'organ_pancreas', price: 18000},
            G: {name: '脾脏', item: 'organ_spleen', price: 12000}
        };

        function displayOrgans(organStatus, scenarioName) {
            const display = document.getElementById('organDisplay');
            const debugInfo = document.getElementById('debugInfo');

            display.innerHTML = `<h3>${scenarioName}</h3>`;
            let debugText = `调试信息 - ${scenarioName}:\n`;
            debugText += `原始数据: ${JSON.stringify(organStatus, null, 2)}\n\n`;

            for (const [key, organ] of Object.entries(organs)) {
                const statusValue = organStatus[organ.item];

                // 使用修复后的逻辑
                let isAvailable = false;

                // 处理布尔值、数字和字符串类型
                if (typeof statusValue === 'boolean') {
                    isAvailable = statusValue;
                } else if (typeof statusValue === 'number') {
                    isAvailable = statusValue === 1;
                } else if (typeof statusValue === 'string') {
                    isAvailable = statusValue === '1' || statusValue.toLowerCase() === 'true';
                } else {
                    // 默认情况下，如果值存在且不是 0、false、'0'、'false'，则认为是可用的
                    isAvailable = statusValue && statusValue !== 0 && statusValue !== '0' && statusValue !== 'false';
                }

                debugText += `${organ.name} (${organ.item}):\n`;
                debugText += `  原始值: ${statusValue} (类型: ${typeof statusValue})\n`;
                debugText += `  可用: ${isAvailable}\n\n`;

                const organItem = document.createElement('div');
                organItem.className = `organ-item ${isAvailable ? 'available' : 'unavailable'}`;
                organItem.innerHTML = `
                    <span>${organ.name}</span>
                    <span class="status-indicator ${isAvailable ? 'healthy' : 'damaged'}">
                        ${isAvailable ? '✓ 健康' : '✗ 已摘除'}
                    </span>
                `;

                display.appendChild(organItem);
            }

            debugInfo.textContent = debugText;
        }

        function testScenario1() {
            const organStatus = {
                organ_heart: 1,
                organ_liver: 1,
                organ_kidney: 1,
                organ_lung: 1,
                organ_pancreas: 1,
                organ_spleen: 1
            };
            displayOrgans(organStatus, '场景1: 所有器官健康 (数字1)');
        }

        function testScenario2() {
            const organStatus = {
                organ_heart: "1",
                organ_liver: "1",
                organ_kidney: "1",
                organ_lung: "1",
                organ_pancreas: "1",
                organ_spleen: "1"
            };
            displayOrgans(organStatus, '场景2: 所有器官健康 (字符串"1")');
        }

        function testScenario3() {
            const organStatus = {
                organ_heart: 0,
                organ_liver: 1,
                organ_kidney: 0,
                organ_lung: 1,
                organ_pancreas: 0,
                organ_spleen: 1
            };
            displayOrgans(organStatus, '场景3: 部分器官摘除 (数字0)');
        }

        function testScenario4() {
            const organStatus = {
                organ_heart: "0",
                organ_liver: "1",
                organ_kidney: "0",
                organ_lung: "1",
                organ_pancreas: "0",
                organ_spleen: "1"
            };
            displayOrgans(organStatus, '场景4: 部分器官摘除 (字符串"0")');
        }

        function testScenario5() {
            const organStatus = {};
            displayOrgans(organStatus, '场景5: 空数据库情况');
        }

        function testScenario6() {
            const organStatus = {
                organ_heart: true,
                organ_liver: true,
                organ_kidney: true,
                organ_lung: true,
                organ_pancreas: true,
                organ_spleen: true
            };
            displayOrgans(organStatus, '场景6: 所有器官健康 (布尔true) - 问题场景');
        }

        function testScenario7() {
            const organStatus = {
                organ_heart: false,
                organ_liver: true,
                organ_kidney: false,
                organ_lung: true,
                organ_pancreas: false,
                organ_spleen: true
            };
            displayOrgans(organStatus, '场景7: 部分器官摘除 (布尔false)');
        }

        // 默认显示场景6（问题场景）
        testScenario6();
    </script>
</body>
</html>
