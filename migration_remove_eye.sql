-- 数据库迁移脚本：移除眼球器官
-- 执行此脚本来更新现有数据库结构

-- 1. 移除 player_organs 表中的 organ_eye 字段
ALTER TABLE `player_organs` DROP COLUMN IF EXISTS `organ_eye`;

-- 2. 移除 items 表中的眼球道具（如果存在）
DELETE FROM `items` WHERE `name` = 'organ_eye';

-- 3. 清理玩家背包中的眼球道具（如果使用 ESX）
-- 注意：根据您的框架调整表名
-- DELETE FROM `user_inventory` WHERE `item` = 'organ_eye';

-- 4. 验证更改
SELECT 'Migration completed: Eye organ removed' as status;

-- 5. 显示更新后的器官表结构
DESCRIBE `player_organs`;

-- 6. 显示剩余的器官道具
SELECT * FROM `items` WHERE `name` LIKE 'organ_%' ORDER BY `name`;
