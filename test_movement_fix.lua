-- 测试移动修复的脚本

-- 测试命令：模拟手术过程
RegisterCommand('testsurgery', function(source, args, rawCommand)
    local duration = tonumber(args[1]) or 5000 -- 默认5秒
    
    print(string.format('^3[测试] ^7开始模拟手术，持续时间: %d毫秒', duration))
    
    -- 触发手术动画
    TriggerClientEvent('organ_trade:startExtraction', source, duration)
    
    -- 在手术完成后检查状态
    CreateThread(function()
        Wait(duration + 1000)
        
        TriggerClientEvent('chat:addMessage', source, {
            color = {0, 255, 0},
            multiline = false,
            args = {"测试系统", "手术模拟完成，请检查是否能正常移动"}
        })
        
        print(string.format('^2[测试] ^7玩家 %d 的手术模拟已完成', source))
    end)
end, false)

-- 测试命令：检查玩家控制状态
RegisterCommand('checkcontrols', function(source, args, rawCommand)
    TriggerClientEvent('organ_trade:checkControlStatus', source)
end, false)

-- 客户端检查控制状态
RegisterNetEvent('organ_trade:checkControlStatus', function()
    local playerPed = PlayerPedId()
    local playerId = PlayerId()
    
    local controlStatus = {
        playerControl = GetPlayerControl(playerId),
        isTaskActive = IsPedActiveInScenario(playerPed),
        currentTask = GetScriptTaskStatus(playerPed, 0x4924437D), -- SCRIPT_TASK_PLAY_ANIM
        isInVehicle = IsPedInAnyVehicle(playerPed, false)
    }
    
    print('^3[控制检查] ^7玩家控制状态:')
    print(string.format('  玩家控制: %s', controlStatus.playerControl and '启用' or '禁用'))
    print(string.format('  任务活跃: %s', controlStatus.isTaskActive and '是' or '否'))
    print(string.format('  动画任务: %d', controlStatus.currentTask))
    print(string.format('  在载具中: %s', controlStatus.isInVehicle and '是' or '否'))
    
    ESX.ShowNotification(string.format(
        '控制状态检查:\n玩家控制: %s\n任务活跃: %s', 
        controlStatus.playerControl and '启用' or '禁用',
        controlStatus.isTaskActive and '是' or '否'
    ), 'info')
end)

-- 强制清理命令（服务端版本）
RegisterCommand('forcecleanup', function(source, args, rawCommand)
    if source == 0 then
        local targetId = tonumber(args[1])
        if not targetId then
            print('^1[错误] ^7请指定玩家ID: forcecleanup <playerid>')
            return
        end
        
        TriggerClientEvent('organ_trade:forceCleanup', targetId)
        print(string.format('^2[清理] ^7已对玩家 %d 执行强制清理', targetId))
    else
        TriggerClientEvent('organ_trade:forceCleanup', source)
    end
end, true)

-- 客户端强制清理
RegisterNetEvent('organ_trade:forceCleanup', function()
    local playerPed = PlayerPedId()
    local playerId = PlayerId()
    
    print('^3[强制清理] ^7开始清理玩家状态...')
    
    -- 清理所有任务
    ClearPedTasks(playerPed)
    ClearPedTasksImmediately(playerPed)
    
    -- 停止所有动画
    StopAnimTask(playerPed, 'amb@medic@standing@kneel@base', 'base', 1.0)
    
    -- 恢复控制
    EnableAllControlActions(0)
    SetPlayerControl(playerId, true, 0)
    
    -- 重置玩家状态
    SetPedCanRagdoll(playerPed, true)
    SetPedCanBeKnockedOffVehicle(playerPed, 1)
    
    print('^2[强制清理] ^7玩家状态已清理完成')
    ESX.ShowNotification('玩家状态已强制清理', 'success')
end)

-- 监控线程：检测卡住的玩家
CreateThread(function()
    while true do
        Wait(30000) -- 每30秒检查一次
        
        local players = GetPlayers()
        for _, playerId in ipairs(players) do
            local playerPed = GetPlayerPed(playerId)
            
            -- 检查玩家是否可能卡住
            if playerPed and DoesEntityExist(playerPed) then
                local isTaskActive = IsPedActiveInScenario(playerPed)
                local taskStatus = GetScriptTaskStatus(playerPed, 0x4924437D)
                
                -- 如果玩家有活跃的手术动画任务超过60秒，可能是卡住了
                if isTaskActive and taskStatus == 1 then
                    print(string.format('^3[监控] ^7检测到玩家 %s 可能卡在手术动画中', GetPlayerName(playerId)))
                    
                    -- 可以选择自动清理或只是记录
                    -- TriggerClientEvent('organ_trade:forceCleanup', playerId)
                end
            end
        end
    end
end)

print('^2[测试脚本] ^7移动修复测试脚本已加载')
print('^3[命令] ^7可用命令:')
print('  /testsurgery [时间] - 模拟手术过程')
print('  /checkcontrols - 检查控制状态')
print('  /fixcontrols - 修复控制问题')
print('  /forcecleanup [玩家ID] - 强制清理玩家状态')
