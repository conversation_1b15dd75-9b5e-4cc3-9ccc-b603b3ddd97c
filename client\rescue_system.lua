-- 求救系统客户端

local canSendSOS = false
local rescueWaypoint = nil
local currentRescueCall = nil

-- 启用SOS功能
RegisterNetEvent('organ_trade:enableSOS', function()
    canSendSOS = true
    lib.notify({
        title = '器官交易系统',
        description = '你现在可以发送求救信号 /sos',
        type = 'info'
    })
end)

-- 发送SOS命令
RegisterCommand(Config.Rescue.command, function()
    if not canSendSOS then
        lib.notify({
            title = '器官交易系统',
            description = '你现在无法发送求救信号',
            type = 'error'
        })
        return
    end

    -- 确认对话框
    local alert = lib.alertDialog({
        header = '发送求救信号',
        content = '确认发送求救信号？',
        centered = true,
        cancel = true
    })

    if alert == 'confirm' then
        TriggerServerEvent('organ_trade:sendSOS')
        canSendSOS = false -- 防止重复发送
    end
end)

-- 接收SOS求救信号（医护人员）
RegisterNetEvent('organ_trade:receiveSOSCall', function(callData)
    -- 创建地图标记
    local blip = AddBlipForCoord(callData.coords.x, callData.coords.y, callData.coords.z)
    SetBlipSprite(blip, Config.Rescue.blip.sprite)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, Config.Rescue.blip.scale)
    SetBlipColour(blip, Config.Rescue.blip.color)
    SetBlipAsShortRange(blip, false)
    BeginTextCommandSetBlipName('STRING')
    AddTextComponentString(string.format('SOS - %s', callData.victimName))
    EndTextCommandSetBlipName(blip)
    
    -- 闪烁效果
    SetBlipFlashes(blip, true)
    
    -- 显示通知
    lib.notify({
        title = '收到求救信号！',
        description = string.format('受害者: %s\n时间: %s', callData.victimName, callData.timestamp),
        type = 'error',
        duration = 10000
    })
    
    -- 播放警报声
    PlaySoundFrontend(-1, 'TIMER_STOP', 'HUD_MINI_GAME_SOUNDSET', 1)
    
    -- 5分钟后自动移除标记
    CreateThread(function()
        Wait(Config.Rescue.response_time)
        if DoesBlipExist(blip) then
            RemoveBlip(blip)
        end
    end)
end)

-- 查看活跃求救列表（医护人员）
function ViewActiveRescues()
    TriggerServerEvent('organ_trade:getActiveRescues')
end

-- 接收活跃求救列表
RegisterNetEvent('organ_trade:receiveActiveRescues', function(rescues)
    if #rescues == 0 then
        lib.notify({
            title = '器官交易系统',
            description = '当前没有活跃的求救信号',
            type = 'info'
        })
        return
    end

    local options = {}

    for _, rescue in ipairs(rescues) do
        local timeText = string.format('%d秒前', rescue.timeAgo)
        if rescue.timeAgo >= 60 then
            timeText = string.format('%d分钟前', math.floor(rescue.timeAgo / 60))
        end

        table.insert(options, {
            title = string.format('%s (%s)', rescue.victimName, timeText),
            description = '点击响应此求救信号',
            onSelect = function()
                -- 确认响应
                local alert = lib.alertDialog({
                    header = '响应求救信号',
                    content = '确认响应该求救信号？',
                    centered = true,
                    cancel = true
                })

                if alert == 'confirm' then
                    TriggerServerEvent('organ_trade:respondToSOS', rescue.id)
                    currentRescueCall = rescue.id
                end
            end
        })
    end

    lib.registerContext({
        id = 'active_rescues_menu',
        title = '活跃求救信号',
        options = options
    })

    lib.showContext('active_rescues_menu')
end)

-- 设置救援导航点
RegisterNetEvent('organ_trade:setRescueWaypoint', function(coords, victimName)
    -- 移除旧的导航点
    if rescueWaypoint then
        RemoveBlip(rescueWaypoint)
    end
    
    -- 创建新的导航点
    rescueWaypoint = AddBlipForCoord(coords.x, coords.y, coords.z)
    SetBlipSprite(rescueWaypoint, 8)
    SetBlipDisplay(rescueWaypoint, 4)
    SetBlipScale(rescueWaypoint, 1.2)
    SetBlipColour(rescueWaypoint, 2)
    SetBlipRoute(rescueWaypoint, true)
    SetBlipRouteColour(rescueWaypoint, 2)
    BeginTextCommandSetBlipName('STRING')
    AddTextComponentString(string.format('救援目标 - %s', victimName))
    EndTextCommandSetBlipName(rescueWaypoint)
    
    lib.notify({
        title = '器官交易系统',
        description = '导航点已设置，请前往救援现场',
        type = 'success'
    })
    
    -- 检查到达现场
    CreateThread(function()
        while rescueWaypoint and DoesBlipExist(rescueWaypoint) do
            local playerCoords = GetEntityCoords(PlayerPedId())
            local distance = GetDistance(playerCoords, coords)
            
            if distance <= 10.0 then
                -- 到达现场
                RemoveBlip(rescueWaypoint)
                rescueWaypoint = nil
                
                lib.notify({
                    title = '器官交易系统',
                    description = '你已到达救援现场',
                    type = 'success'
                })
                TriggerServerEvent('organ_trade:arriveAtScene', currentRescueCall)
                break
            end
            
            Wait(1000)
        end
    end)
end)

-- 显示救援选项
RegisterNetEvent('organ_trade:showRescueOptions', function(callId, victimId)
    local options = {
        {
            title = '使用肾上腺素',
            description = '为受害者注射肾上腺素延长生命',
            onSelect = function()
                TriggerServerEvent('organ_trade:useAdrenaline', callId, victimId)
            end
        },
        {
            title = '带回医院',
            description = '将受害者送往医院治疗',
            onSelect = function()
                TriggerServerEvent('organ_trade:transportToHospital', callId, victimId)
            end
        },
        {
            title = '取消救援',
            description = '取消当前救援任务',
            onSelect = function()
                currentRescueCall = nil
            end
        }
    }

    lib.registerContext({
        id = 'rescue_options_menu',
        title = '救援选项',
        options = options,
        onExit = function()
            currentRescueCall = nil
        end
    })

    lib.showContext('rescue_options_menu')
end)

-- 医护人员命令
RegisterCommand('rescues', function()
    ViewActiveRescues()
end)

-- 添加建议文本
TriggerEvent('chat:addSuggestion', '/sos', '发送求救信号')
TriggerEvent('chat:addSuggestion', '/rescues', '查看活跃求救信号（医护人员）')

-- 导出函数
exports('ViewActiveRescues', ViewActiveRescues)
exports('CanSendSOS', function() return canSendSOS end)

print('^2[器官交易系统] ^7求救系统客户端已加载')
