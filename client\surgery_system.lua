-- 解剖系统客户端

local isInSurgery = false
local surgeryBlip = nil

-- 使用手术刀
function UseSurgeryKnife()
    -- 获取附近被迷晕的玩家
    TriggerServerEvent('organ_trade:getNearbyDruggedPlayers')
end

-- 接收附近被迷晕的玩家
RegisterNetEvent('organ_trade:receiveNearbyDruggedPlayers', function(players)
    if #players == 0 then
        lib.notify({
            title = '器官交易系统',
            description = '附近没有被迷晕的玩家',
            type = 'error'
        })
        return
    end

    -- 创建玩家选择菜单
    local options = {}

    for _, player in ipairs(players) do
        table.insert(options, {
            title = string.format('%s (距离: %.1fm)', player.name, player.distance),
            description = '选择此玩家进行解剖',
            onSelect = function()
                -- 确认对话框
                local alert = lib.alertDialog({
                    header = '确认解剖',
                    content = '确认开始解剖手术？',
                    centered = true,
                    cancel = true
                })

                if alert == 'confirm' then
                    TriggerServerEvent('organ_trade:startSurgery', player.id)
                end
            end
        })
    end

    lib.registerContext({
        id = 'surgery_target_menu',
        title = '选择解剖目标',
        options = options
    })

    lib.showContext('surgery_target_menu')
end)

-- 打开器官选择界面
RegisterNetEvent('organ_trade:openOrganSelection', function(targetId)
    -- 先获取目标玩家的器官状态
    TriggerServerEvent('organ_trade:getPlayerOrganStatus', targetId)
    
    -- 等待器官状态响应
    local organStatus = nil
    local handler = nil
    
    handler = RegisterNetEvent('organ_trade:receiveOrganStatus', function(organs)
        organStatus = organs
        RemoveEventHandler(handler)
    end)
    
    -- 等待响应
    CreateThread(function()
        local timeout = 0
        while not organStatus and timeout < 50 do -- 5秒超时
            Wait(100)
            timeout = timeout + 1
        end
        
        if not organStatus then
            ESX.ShowNotification('获取器官状态失败', 'error')
            return
        end
        
        -- 创建器官选择菜单
        ShowOrganSelectionMenu(targetId, organStatus)
    end)
end)

-- 显示器官选择菜单 (使用HTML UI)
function ShowOrganSelectionMenu(targetId, organStatus)
    -- 使用HTML UI显示器官选择界面
    OpenOrganSelectionUI(targetId, organStatus)
end

-- 开始提取动画
RegisterNetEvent('organ_trade:startExtraction', function(duration)
    local playerPed = PlayerPedId()

    -- 播放手术动画
    RequestAnimDict('amb@medic@standing@kneel@base')
    while not HasAnimDictLoaded('amb@medic@standing@kneel@base') do
        Wait(100)
    end

    TaskPlayAnim(playerPed, 'amb@medic@standing@kneel@base', 'base', 8.0, -8.0, duration, 1, 0, false, false, false)

    -- 显示进度条
    lib.progressBar({
        duration = duration,
        label = '正在提取器官...',
        useWhileDead = false,
        canCancel = false,
        disable = {
            car = true,
            move = true,
            combat = true
        }
    })

    -- 确保动画和控制在完成后正确清理
    CreateThread(function()
        Wait(duration + 100) -- 稍微延迟以确保进度条完成

        -- 停止动画
        ClearPedTasks(playerPed)

        -- 确保控制已恢复（防止卡住）
        EnableAllControlActions(0)


    end)
end)

-- 检查手术状态
function CheckSurgeryStatus()
    TriggerServerEvent('organ_trade:checkSurgeryStatus')
end

-- 接收手术状态响应
RegisterNetEvent('organ_trade:surgeryStatusResponse', function(inSurgery)
    isInSurgery = inSurgery
end)

-- 创建解剖地点标记
CreateThread(function()
    for _, location in ipairs(Config.Surgery.locations) do
        local blip = AddBlipForCoord(location.x, location.y, location.z)
        SetBlipSprite(blip, 310)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.8)
        SetBlipColour(blip, 1)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName('STRING')
        AddTextComponentString(location.name)
        EndTextCommandSetBlipName(blip)
    end
end)

-- 玩家生成时检查状态
AddEventHandler('playerSpawned', function()
    CreateThread(function()
        Wait(2000)
        CheckSurgeryStatus()
    end)
end)

-- 处理器官状态刷新（用于器官摘除后更新UI而不关闭界面）
RegisterNetEvent('organ_trade:refreshOrganUI', function(organs)
    -- 如果UI界面是打开的，刷新器官状态
    SendNUIMessage({
        type = 'refreshOrganStatus',
        organStatus = organs
    })
end)

-- 紧急恢复控制命令（用于调试）
RegisterCommand('fixcontrols', function()
    local playerPed = PlayerPedId()

    -- 清理所有任务和动画
    ClearPedTasks(playerPed)
    ClearPedTasksImmediately(playerPed)

    -- 恢复所有控制
    EnableAllControlActions(0)

    -- 重置玩家状态
    SetPlayerControl(PlayerId(), true, 0)


    ESX.ShowNotification('控制已恢复', 'success')
end, false)

-- 导出函数
exports('UseSurgeryKnife', UseSurgeryKnife)
exports('IsInSurgery', function() return isInSurgery end)

print('^2[器官交易系统] ^7解剖系统客户端已加载')
