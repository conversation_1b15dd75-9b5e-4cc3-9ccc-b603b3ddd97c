-- 修复器官状态布尔值问题的SQL脚本
-- 此脚本将布尔值转换为数字值以确保正确显示

-- 1. 检查当前数据状态
SELECT 'player_organs 表当前数据状态:' as info;
SELECT 
    identifier,
    organ_heart,
    organ_liver,
    organ_kidney,
    organ_lung,
    organ_pancreas,
    organ_spleen,
    typeof(organ_heart) as heart_type,
    typeof(organ_liver) as liver_type
FROM player_organs 
LIMIT 5;

-- 2. 修改表结构，将 tinyint(1) 改为 int(1) 以避免布尔值转换
ALTER TABLE player_organs 
MODIFY COLUMN organ_heart int(1) DEFAULT 1,
MODIFY COLUMN organ_liver int(1) DEFAULT 1,
MODIFY COLUMN organ_kidney int(1) DEFAULT 1,
MODIFY COLUMN organ_lung int(1) DEFAULT 1,
MODIFY COLUMN organ_pancreas int(1) DEFAULT 1,
MODIFY COLUMN organ_spleen int(1) DEFAULT 1;

-- 3. 更新现有数据，将布尔值转换为数字
UPDATE player_organs SET 
    organ_heart = CASE 
        WHEN organ_heart = true OR organ_heart = 1 OR organ_heart = '1' THEN 1 
        ELSE 0 
    END,
    organ_liver = CASE 
        WHEN organ_liver = true OR organ_liver = 1 OR organ_liver = '1' THEN 1 
        ELSE 0 
    END,
    organ_kidney = CASE 
        WHEN organ_kidney = true OR organ_kidney = 1 OR organ_kidney = '1' THEN 1 
        ELSE 0 
    END,
    organ_lung = CASE 
        WHEN organ_lung = true OR organ_lung = 1 OR organ_lung = '1' THEN 1 
        ELSE 0 
    END,
    organ_pancreas = CASE 
        WHEN organ_pancreas = true OR organ_pancreas = 1 OR organ_pancreas = '1' THEN 1 
        ELSE 0 
    END,
    organ_spleen = CASE 
        WHEN organ_spleen = true OR organ_spleen = 1 OR organ_spleen = '1' THEN 1 
        ELSE 0 
    END;

-- 4. 验证修复结果
SELECT 'player_organs 表修复后数据状态:' as info;
SELECT 
    identifier,
    organ_heart,
    organ_liver,
    organ_kidney,
    organ_lung,
    organ_pancreas,
    organ_spleen
FROM player_organs 
LIMIT 10;

-- 5. 检查数据类型分布
SELECT 'organ_heart 值分布:' as info;
SELECT organ_heart, COUNT(*) as count FROM player_organs GROUP BY organ_heart;

SELECT 'organ_liver 值分布:' as info;
SELECT organ_liver, COUNT(*) as count FROM player_organs GROUP BY organ_liver;

-- 6. 确保所有值都是 0 或 1
UPDATE player_organs SET 
    organ_heart = CASE WHEN organ_heart > 0 THEN 1 ELSE 0 END,
    organ_liver = CASE WHEN organ_liver > 0 THEN 1 ELSE 0 END,
    organ_kidney = CASE WHEN organ_kidney > 0 THEN 1 ELSE 0 END,
    organ_lung = CASE WHEN organ_lung > 0 THEN 1 ELSE 0 END,
    organ_pancreas = CASE WHEN organ_pancreas > 0 THEN 1 ELSE 0 END,
    organ_spleen = CASE WHEN organ_spleen > 0 THEN 1 ELSE 0 END
WHERE organ_heart NOT IN (0, 1) 
   OR organ_liver NOT IN (0, 1) 
   OR organ_kidney NOT IN (0, 1) 
   OR organ_lung NOT IN (0, 1) 
   OR organ_pancreas NOT IN (0, 1) 
   OR organ_spleen NOT IN (0, 1);

-- 7. 最终验证
SELECT 'boolean_fix_completed' as status;
SELECT 'Final verification:' as info;
SELECT 
    COUNT(*) as total_records,
    SUM(CASE WHEN organ_heart = 1 THEN 1 ELSE 0 END) as healthy_hearts,
    SUM(CASE WHEN organ_liver = 1 THEN 1 ELSE 0 END) as healthy_livers,
    SUM(CASE WHEN organ_kidney = 1 THEN 1 ELSE 0 END) as healthy_kidneys,
    SUM(CASE WHEN organ_lung = 1 THEN 1 ELSE 0 END) as healthy_lungs,
    SUM(CASE WHEN organ_pancreas = 1 THEN 1 ELSE 0 END) as healthy_pancreas,
    SUM(CASE WHEN organ_spleen = 1 THEN 1 ELSE 0 END) as healthy_spleens
FROM player_organs;
