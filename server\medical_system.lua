-- 医疗救治系统服务器端

local extendedLifePlayers = {} -- 使用肾上腺素延长生命的玩家

-- 使用肾上腺素
RegisterNetEvent('organ_trade:useAdrenaline', function(callId, victimId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xVictim = ESX.GetPlayerFromId(victimId)
    
    if not xPlayer or not xVictim then
        NotifyPlayer(source, '目标玩家不存在', 'error')
        return
    end
    
    -- 检查权限
    if not HasPermission(source, 'medical_response') then
        NotifyPlayer(source, '你没有权限使用肾上腺素', 'error')
        return
    end
    
    -- 检查是否有肾上腺素
    if not HasPlayerItem(source, Config.Medical.adrenaline_item, 1) then
        NotifyPlayer(source, '你没有肾上腺素', 'error')
        return
    end
    
    -- 验证求救记录
    local rescue = exports['qiguan_tudou']:GetActiveRescue(callId)
    if not rescue or rescue.status ~= 'arrived' then
        NotifyPlayer(source, '无效的救援状态', 'error')
        return
    end
    
    -- 检查距离
    local medicCoords = GetPlayerCoords(source)
    local victimCoords = GetPlayerCoords(victimId)
    local distance = GetDistance(medicCoords, victimCoords)
    
    if distance > 5.0 then
        NotifyPlayer(source, '距离受害者太远', 'error')
        return
    end
    
    -- 移除肾上腺素
    RemovePlayerItem(source, Config.Medical.adrenaline_item, 1)
    
    -- 延长受害者生命
    local victimIdentifier = GetPlayerIdentifierByServerId(victimId)
    extendedLifePlayers[victimIdentifier] = {
        playerId = victimId,
        startTime = GetGameTimer(),
        duration = Config.Medical.extend_time,
        medic = xPlayer.getName()
    }
    
    -- 通知双方
    NotifyPlayer(source, Config.Notifications.adrenaline_used, 'success')
    NotifyPlayer(victimId, '医护人员给你注射了肾上腺素，你的生命得到延长', 'success')
    
    -- 发送延长生命效果给受害者
    TriggerClientEvent('organ_trade:applyLifeExtension', victimId, Config.Medical.extend_time)
    
    -- 完成救援
    exports['organ_trade']:CompleteRescue(callId, true)
    
    -- 设置定时器
    CreateThread(function()
        Wait(Config.Medical.extend_time)
        
        if extendedLifePlayers[victimIdentifier] then
            extendedLifePlayers[victimIdentifier] = nil
            
            -- 通知受害者肾上腺素效果结束
            if ESX.GetPlayerFromId(victimId) then
                NotifyPlayer(victimId, '肾上腺素效果即将结束，请尽快前往医院', 'error')
                TriggerClientEvent('organ_trade:removeLifeExtension', victimId)
            end
        end
    end)
    
    -- 记录日志
    LogAction('ADRENALINE_USED', source, victimId, string.format('CallID: %d, Extended for: %dms', callId, Config.Medical.extend_time))
end)

-- 传送到医院
RegisterNetEvent('organ_trade:transportToHospital', function(callId, victimId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xVictim = ESX.GetPlayerFromId(victimId)
    
    if not xPlayer or not xVictim then
        NotifyPlayer(source, '目标玩家不存在', 'error')
        return
    end
    
    -- 检查权限
    if not HasPermission(source, 'medical_response') then
        NotifyPlayer(source, '你没有权限传送患者', 'error')
        return
    end
    
    -- 验证求救记录
    local rescue = exports['qiguan_tudou']:GetActiveRescue(callId)
    if not rescue then
        NotifyPlayer(source, '无效的救援记录', 'error')
        return
    end
    
    -- 选择最近的医院
    local medicCoords = GetPlayerCoords(source)
    local nearestHospital = nil
    local minDistance = math.huge
    
    for _, hospital in ipairs(Config.Medical.hospital_locations) do
        local distance = GetDistance(medicCoords, hospital)
        if distance < minDistance then
            minDistance = distance
            nearestHospital = hospital
        end
    end
    
    if not nearestHospital then
        NotifyPlayer(source, '找不到附近的医院', 'error')
        return
    end
    
    -- 传送双方到医院
    TriggerClientEvent('organ_trade:teleportToHospital', source, nearestHospital)
    TriggerClientEvent('organ_trade:teleportToHospital', victimId, nearestHospital)
    
    -- 通知
    NotifyPlayer(source, string.format('已将患者送往 %s', nearestHospital.name), 'success')
    NotifyPlayer(victimId, string.format('你被送往了 %s', nearestHospital.name), 'info')
    
    -- 完成救援
    exports['qiguan_tudou']:CompleteRescue(callId, true)
    
    -- 记录日志
    LogAction('TRANSPORTED_TO_HOSPITAL', source, victimId, nearestHospital.name)
end)

-- 使用手术台治疗
RegisterNetEvent('organ_trade:useSurgeryTable', function(targetId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = targetId and ESX.GetPlayerFromId(targetId) or xPlayer
    
    if not xPlayer or not xTarget then
        NotifyPlayer(source, '目标玩家不存在', 'error')
        return
    end
    
    -- 检查权限
    if not HasPermission(source, 'medical_response') then
        NotifyPlayer(source, '你没有权限使用手术台', 'error')
        return
    end
    
    -- 检查是否在手术台附近
    local isNear, location = IsPlayerNearLocation(source, Config.Medical.surgery_table, 3.0)
    if not isNear then
        NotifyPlayer(source, '你必须在手术台附近才能进行治疗', 'error')
        return
    end
    
    -- 获取目标玩家的器官状态
    local targetIdentifier = GetPlayerIdentifierByServerId(xTarget.source)
    local organs = GetPlayerOrgans(targetIdentifier)
    
    -- 检查是否有缺失的器官
    local missingOrgans = {}
    for organField, status in pairs(organs) do
        if status == 0 and organField ~= 'id' and organField ~= 'identifier' and organField ~= 'last_updated' then
            for _, organ in pairs(Config.Surgery.organs) do
                if organ.item == organField then
                    table.insert(missingOrgans, {
                        field = organField,
                        name = organ.name,
                        cost = Config.Medical.repair_cost[organField] or 50000
                    })
                    break
                end
            end
        end
    end
    
    if #missingOrgans == 0 then
        NotifyPlayer(source, '患者的器官都很健康，不需要治疗', 'info')
        return
    end
    
    -- 发送治疗选项给医生
    TriggerClientEvent('organ_trade:showSurgeryOptions', source, xTarget.source, missingOrgans)
end)

-- 修复器官
RegisterNetEvent('organ_trade:repairOrgan', function(targetId, organField, cost)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(targetId)
    
    if not xPlayer or not xTarget then
        NotifyPlayer(source, '目标玩家不存在', 'error')
        return
    end
    
    -- 检查权限
    if not HasPermission(source, 'medical_response') then
        NotifyPlayer(source, '你没有权限进行器官修复', 'error')
        return
    end
    
    -- 检查医院资金（这里可以根据实际情况调整）
    if xPlayer.getMoney() < cost then
        NotifyPlayer(source, '医院资金不足，无法进行器官修复', 'error')
        return
    end
    
    -- 扣除费用
    xPlayer.removeMoney(cost)
    
    -- 修复器官
    local targetIdentifier = GetPlayerIdentifierByServerId(targetId)
    RepairPlayerOrgan(targetIdentifier, organField)
    
    -- 获取器官名称
    local organName = '未知器官'
    for _, organ in pairs(Config.Surgery.organs) do
        if organ.item == organField then
            organName = organ.name
            break
        end
    end
    
    -- 通知
    NotifyPlayer(source, string.format(Config.Notifications.organ_repaired, organName), 'success')
    NotifyPlayer(targetId, string.format('你的%s已经被修复', organName), 'success')
    
    -- 记录日志
    LogAction('ORGAN_REPAIRED', source, targetId, string.format('%s - Cost: $%d', organName, cost))
end)

-- 检查生命延长状态
function IsLifeExtended(playerId)
    local identifier = GetPlayerIdentifierByServerId(playerId)
    return extendedLifePlayers[identifier] ~= nil
end

-- 获取附近需要治疗的玩家
RegisterNetEvent('organ_trade:getNearbyPatients', function()
    local source = source
    local sourceCoords = GetPlayerCoords(source)
    local nearbyPatients = {}
    
    local allPlayers = GetOnlinePlayersExcept(source)
    
    for _, player in ipairs(allPlayers) do
        local targetCoords = GetPlayerCoords(player.id)
        local distance = GetDistance(sourceCoords, targetCoords)
        
        if distance <= 5.0 then
            -- 检查是否需要治疗
            local identifier = GetPlayerIdentifierByServerId(player.id)
            local organs = GetPlayerOrgans(identifier)
            
            local needsTreatment = false
            for organField, status in pairs(organs) do
                if status == 0 and organField ~= 'id' and organField ~= 'identifier' and organField ~= 'last_updated' then
                    needsTreatment = true
                    break
                end
            end
            
            if needsTreatment then
                table.insert(nearbyPatients, {
                    id = player.id,
                    name = player.name,
                    distance = math.floor(distance * 100) / 100
                })
            end
        end
    end
    
    TriggerClientEvent('organ_trade:receiveNearbyPatients', source, nearbyPatients)
end)

-- 导出函数
exports('IsLifeExtended', IsLifeExtended)

print('^2[器官交易系统] ^7医疗救治系统模块已加载')
