-- 求救系统服务器端

-- 发送SOS求救信号
RegisterNetEvent('organ_trade:sendSOS', function()
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if not xPlayer then return end
    
    local identifier = xPlayer.identifier
    local playerCoords = GetPlayerCoords(source)
    
    -- 检查是否已经有活跃的求救
    for callId, rescue in pairs(activeRescues) do
        if rescue.victim == identifier and rescue.status == 'active' then
            NotifyPlayer(source, '你已经发送过求救信号', 'error')
            return
        end
    end
    
    -- 创建求救记录
    local callId = CreateRescueCall(identifier, playerCoords)
    
    if not callId then
        NotifyPlayer(source, '发送求救信号失败', 'error')
        return
    end
    
    -- 添加到活跃求救列表
    activeRescues[callId] = {
        id = callId,
        victim = identifier,
        victimId = source,
        victimName = xPlayer.getName(),
        coords = playerCoords,
        status = 'active',
        timestamp = GetGameTimer(),
        blipId = nil
    }
    
    -- 通知受害者
    NotifyPlayer(source, Config.Notifications.sos_sent, 'success')
    
    -- 通知医护人员
    local medicMessage = string.format('收到求救信号！受害者: %s', xPlayer.getName())
    NotifyJob(Config.Rescue.jobs, medicMessage, 'error')
    
    -- 发送求救信息给医护人员
    local xPlayers = ESX.GetExtendedPlayers()
    for _, medic in pairs(xPlayers) do
        for _, job in ipairs(Config.Rescue.jobs) do
            if medic.job.name == job then
                TriggerClientEvent('organ_trade:receiveSOSCall', medic.source, {
                    callId = callId,
                    victimName = xPlayer.getName(),
                    coords = playerCoords,
                    timestamp = os.date('%H:%M:%S')
                })
                break
            end
        end
    end
    
    -- 设置5分钟超时
    CreateThread(function()
        Wait(Config.Rescue.response_time)
        
        if activeRescues[callId] and activeRescues[callId].status == 'active' then
            -- 超时处理
            activeRescues[callId].status = 'expired'
            UpdateRescueCall(callId, 'expired', nil)
            
            NotifyPlayer(source, '求救信号已超时，没有医护人员响应', 'error')
            
            -- 清理求救记录
            CreateThread(function()
                Wait(60000) -- 1分钟后清理
                if activeRescues[callId] then
                    activeRescues[callId] = nil
                end
            end)
        end
    end)
    
    -- 记录日志
    LogAction('SOS_SENT', source, nil, string.format('Coords: %.2f, %.2f, %.2f', playerCoords.x, playerCoords.y, playerCoords.z))
end)

-- 医护人员响应求救
RegisterNetEvent('organ_trade:respondToSOS', function(callId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if not xPlayer then return end
    
    -- 检查职业权限
    local hasPermission = false
    for _, job in ipairs(Config.Rescue.jobs) do
        if xPlayer.job.name == job then
            hasPermission = true
            break
        end
    end
    
    if not hasPermission then
        NotifyPlayer(source, '你没有权限响应求救', 'error')
        return
    end
    
    -- 检查求救是否存在且活跃
    if not activeRescues[callId] or activeRescues[callId].status ~= 'active' then
        NotifyPlayer(source, '该求救信号已失效', 'error')
        return
    end
    
    local rescue = activeRescues[callId]
    
    -- 更新求救状态
    rescue.status = 'responded'
    rescue.responder = xPlayer.identifier
    rescue.responderName = xPlayer.getName()
    rescue.responderId = source
    
    UpdateRescueCall(callId, 'responded', xPlayer.identifier)
    
    -- 通知医护人员
    NotifyPlayer(source, string.format('你已响应 %s 的求救信号', rescue.victimName), 'success')
    
    -- 通知受害者
    if rescue.victimId and ESX.GetPlayerFromId(rescue.victimId) then
        NotifyPlayer(rescue.victimId, string.format('医护人员 %s 正在赶来救援', xPlayer.getName()), 'success')
    end
    
    -- 创建导航点给医护人员
    TriggerClientEvent('organ_trade:setRescueWaypoint', source, rescue.coords, rescue.victimName)
    
    -- 记录日志
    LogAction('SOS_RESPONDED', source, rescue.victimId, string.format('CallID: %d, Victim: %s', callId, rescue.victimName))
end)

-- 到达现场
RegisterNetEvent('organ_trade:arriveAtScene', function(callId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if not xPlayer then return end
    
    -- 验证求救记录
    if not activeRescues[callId] or activeRescues[callId].status ~= 'responded' then
        NotifyPlayer(source, '无效的求救记录', 'error')
        return
    end
    
    local rescue = activeRescues[callId]
    
    -- 检查是否是响应者
    if rescue.responder ~= xPlayer.identifier then
        NotifyPlayer(source, '你不是该求救的响应者', 'error')
        return
    end
    
    -- 检查距离
    local medicCoords = GetPlayerCoords(source)
    local distance = GetDistance(medicCoords, rescue.coords)
    
    if distance > 10.0 then
        NotifyPlayer(source, '你需要更靠近受害者', 'error')
        return
    end
    
    -- 更新状态
    rescue.status = 'arrived'
    
    -- 通知医护人员可以使用肾上腺素
    NotifyPlayer(source, '你已到达现场，可以使用肾上腺素救治受害者', 'success')
    
    -- 通知受害者
    if rescue.victimId and ESX.GetPlayerFromId(rescue.victimId) then
        NotifyPlayer(rescue.victimId, '医护人员已到达现场！', 'success')
    end
    
    -- 发送救治界面
    TriggerClientEvent('organ_trade:showRescueOptions', source, callId, rescue.victimId)
    
    -- 记录日志
    LogAction('MEDIC_ARRIVED', source, rescue.victimId, string.format('CallID: %d', callId))
end)

-- 获取活跃的求救列表
RegisterNetEvent('organ_trade:getActiveRescues', function()
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if not xPlayer then return end
    
    -- 检查权限
    local hasPermission = false
    for _, job in ipairs(Config.Rescue.jobs) do
        if xPlayer.job.name == job then
            hasPermission = true
            break
        end
    end
    
    if not hasPermission then
        return
    end
    
    -- 收集活跃的求救
    local rescueList = {}
    for callId, rescue in pairs(activeRescues) do
        if rescue.status == 'active' then
            table.insert(rescueList, {
                id = callId,
                victimName = rescue.victimName,
                coords = rescue.coords,
                timestamp = rescue.timestamp,
                timeAgo = math.floor((GetGameTimer() - rescue.timestamp) / 1000)
            })
        end
    end
    
    TriggerClientEvent('organ_trade:receiveActiveRescues', source, rescueList)
end)

-- 完成救援
function CompleteRescue(callId, success)
    if not activeRescues[callId] then return end
    
    local rescue = activeRescues[callId]
    rescue.status = success and 'completed' or 'failed'
    
    UpdateRescueCall(callId, rescue.status, rescue.responder)
    
    -- 清理记录
    CreateThread(function()
        Wait(300000) -- 5分钟后清理
        if activeRescues[callId] then
            activeRescues[callId] = nil
        end
    end)
end

-- 导出函数
exports('CompleteRescue', CompleteRescue)
exports('GetActiveRescue', function(callId)
    return activeRescues[callId]
end)

print('^2[器官交易系统] ^7求救系统模块已加载')
